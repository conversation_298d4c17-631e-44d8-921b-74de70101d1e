#!/usr/bin/env python3
"""
测试兑换码页面功能
"""

import requests
import json

def test_redeem_page():
    """测试兑换码页面"""
    base_url = "http://localhost:7799"
    
    # 创建会话
    session = requests.Session()
    
    print("=== 兑换码页面功能测试 ===")
    
    # 1. 测试未登录访问兑换码页面（应该重定向到首页）
    print("\n1. 测试未登录访问兑换码页面...")
    response = session.get(f"{base_url}/redeem")
    if response.status_code == 200 and "登录" in response.text:
        print("✓ 未登录用户被正确重定向到首页")
    else:
        print(f"✗ 未登录访问处理异常，状态码: {response.status_code}")
    
    # 2. 登录测试用户
    print("\n2. 登录测试用户...")
    login_data = {
        "username": "user1",
        "password": "123456"
    }
    response = session.post(f"{base_url}/login", json=login_data)
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print(f"✓ 登录成功: {result.get('message')}")
            user_points = result.get('user', {}).get('points', 0)
            print(f"  当前积分: {user_points}")
        else:
            print(f"✗ 登录失败: {result.get('message')}")
            return
    else:
        print(f"✗ 登录请求失败，状态码: {response.status_code}")
        return
    
    # 3. 访问兑换码页面
    print("\n3. 访问兑换码页面...")
    response = session.get(f"{base_url}/redeem")
    if response.status_code == 200 and "兑换码" in response.text:
        print("✓ 成功访问兑换码页面")
    else:
        print(f"✗ 访问兑换码页面失败，状态码: {response.status_code}")
        return
    
    # 4. 测试兑换码功能
    print("\n4. 测试兑换码功能...")
    test_codes = ["CW8LSENJVS3U", "Z6SHW5YFVUSW", "3R3CJ68QBNVD"]  # 使用现有的有效兑换码
    
    for i, code in enumerate(test_codes):
        print(f"\n  测试兑换码 {i+1}: {code}")
        redeem_data = {"code": code}
        response = session.post(f"{base_url}/redeem_code", json=redeem_data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                points_gained = result.get('points_gained', 0)
                new_points = result.get('new_points', 0)
                print(f"  ✓ 兑换成功！获得 {points_gained} 积分，当前积分: {new_points}")
            else:
                print(f"  ✗ 兑换失败: {result.get('message')}")
        else:
            print(f"  ✗ 兑换请求失败，状态码: {response.status_code}")
    
    # 5. 测试重复使用兑换码
    print("\n5. 测试重复使用兑换码...")
    redeem_data = {"code": test_codes[0]}  # 使用第一个已经用过的兑换码
    response = session.post(f"{base_url}/redeem_code", json=redeem_data)
    
    if response.status_code == 200:
        result = response.json()
        if not result.get('success'):
            print(f"  ✓ 正确阻止重复使用: {result.get('message')}")
        else:
            print(f"  ✗ 重复使用检查失败，不应该成功")
    else:
        print(f"  ✗ 重复使用测试请求失败，状态码: {response.status_code}")
    
    # 6. 测试无效兑换码
    print("\n6. 测试无效兑换码...")
    invalid_codes = ["INVALID123", "NOTEXIST", ""]
    
    for code in invalid_codes:
        print(f"  测试无效兑换码: '{code}'")
        redeem_data = {"code": code}
        response = session.post(f"{base_url}/redeem_code", json=redeem_data)
        
        if response.status_code == 200:
            result = response.json()
            if not result.get('success'):
                print(f"    ✓ 正确拒绝无效兑换码: {result.get('message')}")
            else:
                print(f"    ✗ 无效兑换码检查失败，不应该成功")
        else:
            print(f"    ✗ 无效兑换码测试请求失败，状态码: {response.status_code}")
    
    # 7. 测试兑换历史
    print("\n7. 测试兑换历史...")
    response = session.get(f"{base_url}/user/redemption_history")
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            records = result.get('records', [])
            print(f"  ✓ 成功获取兑换历史，共 {len(records)} 条记录")
            for record in records[:3]:  # 只显示前3条
                print(f"    - {record.get('code')}: +{record.get('points')} 积分")
        else:
            print(f"  ✗ 获取兑换历史失败: {result.get('message')}")
    else:
        print(f"  ✗ 兑换历史请求失败，状态码: {response.status_code}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_redeem_page()
